<template>
<div class="music-list">
    <div class="title">
        <a href="javascript:;">推荐歌单 <ArrowRight class="ArrowRight"/></a>
    </div>
    <div class="flex-container">
        <div class="flex-item" v-for="(column, columnIndex) in musicColumns" :key="columnIndex">
            <a href="javascript:;" class="item-content" v-for="item in column" :key="item.id">
                {{ item.name }}
            </a>
        </div>
    </div>
</div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import {
  ArrowRight,
} from '@element-plus/icons-vue'

// 音乐列表数据
const musiclist = ref([
  {
    id: 1,
    name: '推荐歌单1',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 2,
    name: '推荐歌单2',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 3,
    name: '推荐歌单3',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 4,
    name: '推荐歌单4',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 5,
    name: '推荐歌单5',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 6,
    name: '推荐歌单6',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 7,
    name: '推荐歌单7',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 8,
    name: '推荐歌单8',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
])

// 将数据按列分组，每列4个元素
const musicColumns = computed(() => {
  const columns = []
  const itemsPerColumn = 4

  for (let i = 0; i < musiclist.value.length; i += itemsPerColumn) {
    columns.push(musiclist.value.slice(i, i + itemsPerColumn))
  }

  return columns
})

</script>

<style scoped>

.music-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  /* font-size: 18px; */
  /* font-weight: bold; */
  /* margin-bottom: 10px; */
  padding-bottom: 5px;
}
.title a {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  text-decoration: none;
}
.ArrowRight {
    width: 18px;
    height: 18px;
}

/* 弹性盒子容器 - 自动调节列数，从左开始排列 */
.flex-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 15px;
  width: 100%;
}

/* 每个弹性盒子项 - 纵向排列4个元素，固定宽度55px */
.flex-item {
  display: flex;
  flex-direction: column;
  width: 55px;
  flex-shrink: 0;
  gap: 10px;
  padding: 10px 0px 0px 0px;
  /* background-color: #f8f9fa; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}
.flex-item a {
    text-decoration: none;
    color: #000;
}

/* 盒子内的内容元素 */
.item-content {
  padding: 8px 4px;
  width: 100%;
  box-sizing: border-box;
  font-size: 12px;

  /* background-color: #ffffff; */

  border-top: 1px solid #e0e0e0;

  text-align: center;
  transition: all 0.3s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* .item-content:hover {
  background-color: #f0f0f0;
  border-color: #d0d0d0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
} */

</style>
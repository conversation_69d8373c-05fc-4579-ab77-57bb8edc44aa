<template>
<div class="music-list"  id="app">
    <div class="title">
        <a href="javascript:;">推荐歌单 <ArrowRight class="ArrowRight"/></a>
    </div>
    <div class="flex-container">
        <div class="flex-item" v-for="item in musiclist" :key="item.id">
        <a href="javascript:;" class="item-content">{{ item.name }}</a>
        </div>
        <!-- </div>
        <div class="flex-item">
        <a href="javascript:;" class="item-content">内容2-1</a>
        <a href="javascript:;" class="item-content">内容2-2</a>
        <a href="javascript:;" class="item-content">内容2-3</a>
        <a href="javascript:;" class="item-content">内容2-4</a>
        </div>
        <div class="flex-item">
        <a href="javascript:;" class="item-content">内容3-1</a>
        <a href="javascript:;" class="item-content">内容3-2</a>
        <a href="javascript:;" class="item-content">内容3-3</a>
        <a href="javascript:;" class="item-content">内容3-4</a>
        </div> -->
    </div>
</div>
</template>

<script lang="ts" setup>
import {
  ArrowRight,
} from '@element-plus/icons-vue'
import { createApp } from 'vue';
import App from '../App.vue';

const app = createApp(App) 
app.mount('#app')

const musiclist = [
  {
    id: 1,
    name: '推荐歌单1',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 2,
    name: '推荐歌单2',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 3,
    name: '推荐歌单3',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 4,
    name: '推荐歌单4',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
];


// 在 Composition API 中，导入的组件会自动注册，无需手动注册

</script>

<style scoped>

.music-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  /* font-size: 18px; */
  /* font-weight: bold; */
  /* margin-bottom: 10px; */
  padding-bottom: 5px;
}
.title a {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  text-decoration: none;
}
.ArrowRight {
    width: 18px;
    height: 18px;
}

/* 弹性盒子容器 - 横向排列3个元素 */
.flex-container {
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  /* margin-top: 10px; */
  gap: 15px;
  width: 100%;
}

/* 每个弹性盒子项 - 纵向排列4个元素 */
.flex-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 10px;
  padding: 10px 0px 0px 0px;
  /* background-color: #f8f9fa; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}
.flex-item a {
    text-decoration: none;
    color: #000;
}

/* 盒子内的内容元素 */
.item-content {
  padding: 10px 14px;

  /* background-color: #ffffff; */

  border-top: 1px solid #e0e0e0;
 
  text-align: center;
  transition: all 0.3s ease;
}

/* .item-content:hover {
  background-color: #f0f0f0;
  border-color: #d0d0d0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
} */

</style>